import { useAuthStore } from '@/stores/auth';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';

export function Dashboard() {
  const { user, logout } = useAuthStore();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <Button onClick={handleLogout} variant="outline">
            Logout
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Selamat Datang!</CardTitle>
            <CardDescription>
              Anda berhasil login ke sistem KOL
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Nama:</strong> {user?.name}</p>
              <p><strong>Email:</strong> {user?.email || 'Tidak ada'}</p>
              <p><strong>Nomor Telepon:</strong> {user?.phone_number || 'Tidak ada'}</p>
              <p><strong>Status:</strong> {user?.status === 1 ? 'Aktif' : 'Tidak Aktif'}</p>
              <p><strong>Email Terverifikasi:</strong> {user?.is_email_verified ? 'Ya' : 'Tidak'}</p>
              <p><strong>Telepon Terverifikasi:</strong> {user?.is_phone_verified ? 'Ya' : 'Tidak'}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
